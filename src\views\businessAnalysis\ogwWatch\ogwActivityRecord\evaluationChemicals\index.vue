<template>
  <div class="evaluation-chemicals">
    <div class="search-box">
      <OgwSearch
        :fields="searchFields"
        v-model="searchForm"
        @search="handleSearch"
      ></OgwSearch>
      <div>
        <el-button type="primary" @click="handleUpload">上传筛选表</el-button>
        <el-button type="primary" @click="handleAdd">手工新增</el-button>
      </div>
    </div>
    <div>
      <OgwTable
        :columns="columns"
        :data="tableData"
        :show-index="true"
        :page-size="10"
        :current-page="1"
        :showActions="true"
        :actionColumnWidth="120"
        :loading="loading"
      >
        <template #applyFileId="{ row, $index }">
          <div @click="handleCellClick(row, $index)">查看</div>
        </template>
        <template #reportFileId="{ row, $index }">
          <div @click="checkReport(row, $index)">查看</div>
        </template>
        <template #state="{ row, $index }">
          <div v-if="row.state === 1" class="saved">已保存</div>
          <div v-else-if="row.state === 2" class="submitted">已提交</div>
          <div v-else>未保存</div>
        </template>

        <!-- 自定义操作列插槽 -->
        <template #actions="{ row, $index }">
          <el-button
            v-if="row.state !== 2"
            type="text"
            size="mini"
            @click="saveRow(row)"
            >保存</el-button
          >
          <el-button type="text" size="mini" @click="deleteRow(row, $index)"
            >删除</el-button
          >
          <el-button
            v-if="row.state !== 2"
            type="text"
            size="mini"
            @click="editRow(row)"
            >提交</el-button
          >
        </template>
      </OgwTable>
    </div>
  </div>
</template>
<script>
import OgwSearch from "@/components/comTable/OgwSearch.vue";
import OgwTable from "@/components/comTable/OgwTable.vue";
import {
  getFilterChemicals,
  saveFilterChemicals,
  submitFilterChemicals,
  deleteFilterChemicals,
} from "@/api/ogwActiveRecord/chemicalsServe.js";
import { getProd } from "@/api/common.js";
import { cloneDeep } from "lodash";
export default {
  name: "evaluation-chemicals",
  components: {
    OgwSearch,
    OgwTable,
  },
  created() {
    const dateArr = [];
    dateArr[0] = new Date().getFullYear() + "-" + Number(1);
    dateArr[1] = new Date().getFullYear() + "-" + new Date().getMonth();
    this.searchForm.monthRange = dateArr;
  },
  mounted() {
    this.getTableData();
    this.getProdList();
  },
  watch: {
    searchForm: {
      handler(val) {
        this.deviceOptions = this.orgList.map((item) => {
          if (item.orgId === val.orgId) {
            return item.children;
          }
        });
        this.searchFields[1].options = this.deviceOptions.flat(Infinity);
      },
      deep: true,
    },
  },
  computed: {
    searchFields() {
      return [
        {
          label: "组织机构:",
          prop: "orgId",
          type: "select",
          options: this.orgList.map((item) => {
            return {
              label: item.orgName,
              value: item.orgId,
            };
          }),
        },
        {
          label: "平台名称:",
          prop: "deviceNameCode",
          type: "select",
          options: [],
        },
        {
          prop: "monthRange",
          type: "monthrange",
          startPlaceholder: "开始月份",
          endPlaceholder: "结束月份",
        },
      ];
    },
  },
  data() {
    return {
      loading: false,
      orgList: [],
      deviceOptions: [],
      platSelOptions: [],
      searchForm: {
        deptId: "",
        deviceNameCode: "",
        monthRange: ["", ""],
      },
      columns: [
        { label: "申请表", prop: "applyFileId" },
        { label: "报告及专家意见", prop: "reportFileId" },
        {
          label: "平台名称",
          prop: "productionName",
          editable: true,
          options:[]
        },
        { label: "服务项目名称", prop: "projectName", editable: true },
        { label: "原药剂型号", prop: "chemicalType", editable: true },
        {
          label: "原药剂供应商",
          prop: "supplier",
          editable: true,
        },
        {
          label: "药剂单价(元/公斤)",
          prop: "price",
          editable: true,
        },
        {
          label: "药剂年度消耗量(公斤)",
          prop: "yearConsume",
          editable: true,
        },
        {
          label: "药剂注入浓度/ppm",
          prop: "concentration",
          editable: true,
        },
        {
          label: "开展评价理由",
          prop: "reason",
          editable: true,
        },
        {
          label: "服务时间",
          prop: "serviceTime",
          formatter: (row) => {
            return row?.serviceTime?.split(" ")[0];
          },
          editable: true,
        },
        { label: "状态", prop: "state" },
        {
          label: "创建时间",
          prop: "createTime",
          formatter: (row) => {
            return row?.createTime?.split(" ")[0];
          },
        },
      ],
      columnsTem: {
        applyFileId: "",
        reportFileId: "",
        productionName: "",
        projectName: "",
        chemicalType: "",
        supplier: "",
        price: "",
        yearConsume: "",
        concentration: "",
        reason: "",
        serviceTime: "",
        state: "",
        createTime: "",
      },
      tableData: [],
      applyName: "apply",
      opinionName: "opinion",
      fileName: "file",
    };
  },
  methods: {
    handleCellClick(row, index) {
      const id = row.id || null;
      const fileIds = row.applyFileId || null;
      this.$router.push({
        name: "chemicalForm",
        params: { id, fileIds, type: "evaluate" },
      });
    },
    handleUpload() {
      this.$router.push({
        name: "chemicalForm",
        params: { type: "evaluate" },
      });
    },
    handleAdd() {
      this.tableData.push(cloneDeep(this.columnsTem));
    },
    checkReport(row, index) {
      const id = row.id || null;
      const fileIds = row.reportFileId || null;
      this.$router.push({
        name: "opinionsPre",
        params: { id, fileIds, type: "evaluate" },
      });
    },
    saveRow(row) {
      this.saveTableRow(row);
    },
    deleteRow(row, index) {
      if (row.id) {
        this.deleteTableRow(row.id);
      } else {
        this.tableData.splice(index, 1);
      }
    },
    editRow(row) {
      this.editTableRow(row);
    },
    handleSearch(value) {
      this.searchForm.deptId = value?.deptId;
      this.searchForm.deviceNameCode = value?.deviceNameCode;
      this.searchForm.monthRange = value.monthRange;
      this.getTableData();
    },

    async getTableData() {
      this.loading = true;
      const searchInfo = {
        deptId: this.searchForm.deptId || null,
        productionUnitId: this.searchForm.deviceNameCode || null,
        startDate: this.searchForm.monthRange[0] || null,
        endDate: this.searchForm.monthRange[1] || null,
        type: 2,
      };
      const res = await getFilterChemicals(searchInfo);
      if (res.code === 200) {
        this.tableData = res.data;
      }
      this.loading = false;
    },

    async saveTableRow(data) {
      data.type = 2;
      data.hzNo = this.platSelOptions.find(item => item.label === data.productionName).nValue;
      const res = await saveFilterChemicals(data);
      if (res.code === 200) {
        this.$message.success("成功");
        this.getTableData();
      }
    },

    async editTableRow(data) {
      data.type = 2;
      const res = await submitFilterChemicals(data);
      if (res.code === 200) {
        this.$message.success("成功");
        this.getTableData();
      }
    },

    async deleteTableRow(id) {
      const res = await deleteFilterChemicals({ id });
      if (res.code === 200) {
        this.$message.success("删除成功");
        this.getTableData();
      }
    },

    async getProdList() {
      const res = await getProd();
      if (res.code === 200) {
        this.orgList = res.data.map((item) => ({
          orgId: item.orgId,
          orgName: item.orgName,
          children: item.children.map((child) => ({
            value: child.hzNo,
            label: child.name,
          })),
        }));
        this.platSelOptions = res.data.map((item) => {
          return item.children.map((child) => {
            return { label: child.name, value: child.name,nValue:child.hzNo };
          });
        }).flat();
        this.columns[2].options = this.platSelOptions;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.evaluation-chemicals {
  padding: 16px;

  .search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.submitted {
  color: #00b42a;
}

.saved {
  color: #1677ff;
}
</style>
