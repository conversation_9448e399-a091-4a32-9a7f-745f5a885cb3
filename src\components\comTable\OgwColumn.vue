<!--表单表格列 -->
<template>
  <el-table-column
    :label="column.label"
    :prop="column.prop"
    :width="column.width"
    :min-width="column.minWidth || 120"
    :fixed="column.fixed"
    :align="column.align || 'center'"
  >
    <!-- 如果有子列，递归渲染 -->
    <template v-if="column.children && column.children.length">
      <OgwColumn
        v-for="child in column.children"
        :key="child.prop"
        :column="child"
        :show-index="showIndex"
        :summary-config="summaryConfig"
        :fixed="column.fixed"
        @enter-edit="$emit('enter-edit', $event)"
        @exit-edit="$emit('exit-edit', $event)"
        @cell-change="$emit('cell-change', $event)"
        @cell-click="$emit('cell-click', $event)"
      >
        <!-- 透传插槽 -->
        <template v-for="(_, slotName) in $scopedSlots" #[slotName]="scope">
          <slot :name="slotName" v-bind="scope" />
        </template>
      </OgwColumn>
    </template>

    <!-- 没有子列，渲染单元格内容 -->
    <template v-if="!column.children" v-slot="scope">
      <template v-if="scope.row._isSummaryRow">
        <template
          v-if="isFirstSummaryTextColumn(scope.row._summaryType, column.prop)"
        >
          <span class="summary-text">{{ scope.row._summaryText }}</span>
        </template>
        <template v-else-if="summaryConfig.sumColumns.includes(column.prop)">
          <span class="summary-value">{{ scope.row[column.prop] }}</span>
        </template>
      </template>

      <template v-else>
        <!-- 编辑模式 -->
        <template
          v-if="column.editable && isEditingCell(scope.$index, column.prop)"
        >
          <div class="editing-input" @click.stop>
            <!-- 下拉选择器 -->
            <el-select
              v-if="column.options"
              v-model="scope.row[column.prop]"
              size="small"
              style="width: 100%"
              @change="handleEditChange(scope.row, column.prop, scope.$index, $event)"
              @blur="handleEditBlur"
            >
              <el-option
                v-for="opt in column.options"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="column.editType === 'date'"
              ref="datePicker"
              v-model="scope.row[column.prop]"
              :type="getDatePickerType(column)"
              :format="getDateFormat(column)"
              :value-format="getDateValueFormat(column)"
              :placeholder="getDatePlaceholder(column)"
              size="small"
              style="width: 100%"
              :clearable="false"
              :append-to-body="true"
              :popper-class="'table-date-picker'"
              @change="handleEditChange(scope.row, column.prop, scope.$index, $event)"
              @blur="handleDatePickerBlur"
              @focus="handleDatePickerFocus"
            />
            <!-- 其他编辑组件 -->
            <component
              v-else
              :is="getEditComponent(column)"
              v-model="scope.row[column.prop]"
              size="small"
              @blur="handleEditBlur"
              @keyup.enter="handleEditEnter"
              @change="handleEditChange(scope.row, column.prop, scope.$index, $event)"
              style="width: 100%"
            />
          </div>
        </template>

        <!-- 可编辑但未在编辑状态 -->
        <template v-else-if="column.editable">
          <span
            class="editable-cell"
            @click="handleEditableClick($event, scope.$index, column.prop)"
          >
            {{ getFormattedValue(column, scope.row) || "-" }}
          </span>
        </template>

        <!-- 可点击但不可编辑 -->
        <template v-else-if="column.clickable">
          <span
            class="clickable-cell"
            @click="handleClickableClick($event, scope.row, column.prop, scope.$index)"
          >
            <slot :name="column.prop" v-bind="scope">
              {{ getFormattedValue(column, scope.row) }}
            </slot>
          </span>
        </template>

        <!-- 普通单元格 -->
        <template v-else>
          <span>
            <slot :name="column.prop" v-bind="scope">
              {{ getFormattedValue(column, scope.row) }}
            </slot>
          </span>
        </template>
      </template>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: "OgwColumn",
  props: {
    column: Object,
    showIndex: Boolean,
    summaryConfig: Object,
    editingCell: {
      type: Object,
      default: () => ({ rowIndex: null, prop: null }),
    },
  },
  methods: {
    isFirstSummaryTextColumn(type, prop) {
      const mergeCols =
        type === "grand"
          ? this.summaryConfig.grandTotalTextMergeColumns
          : type === "sub"
          ? this.summaryConfig.subTotalTextMergeColumns
          : type === "customSub"
          ? this.summaryConfig.customSubTotalTextMergeColumns
          : type === "customGrand"
          ? this.summaryConfig.customGrandTotalTextMergeColumns
          : [];
      return mergeCols.length && mergeCols[0] === prop;
    },
    isEditingCell(index, prop) {
      return (
        this.editingCell.rowIndex === index && this.editingCell.prop === prop
      );
    },
    getFormattedValue(col, row) {
      const value = row[col.prop];
      if (col.formatter) {
        return col.formatter(row, value);
      }
      if (col.options) {
        const match = col.options.find((opt) => opt.value === value);
        return match ? match.label : value;
      }
      // 日期类型的格式化显示
      if (col.editType === 'date' && value) {
        return this.formatDateValue(value, col);
      }
      return value;
    },
    formatDateValue(value, column) {
      if (!value) return '';

      try {
        const date = new Date(value);
        if (isNaN(date.getTime())) return value;

        const type = this.getDatePickerType(column);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return type === 'month' ? `${year}年${month}月` : `${year}-${month}-${day}`;
      } catch (error) {
        console.warn('日期格式化失败:', error);
        return value;
      }
    },
    getEditComponent(column) {
      // 根据列配置返回对应的编辑组件
      if (column.editComponent) {
        return column.editComponent;
      }
      // 默认使用 el-input
      return 'el-input';
    },
    // 日期选择器相关方法
    getDatePickerType(column) {
      return column.dateConfig?.type || 'date';
    },
    getDateFormat(column) {
      const type = this.getDatePickerType(column);
      if (column.dateConfig?.format) {
        return column.dateConfig.format;
      }
      // 默认格式
      return type === 'month' ? 'yyyy年MM月' : 'yyyy-MM-dd';
    },
    getDateValueFormat(column) {
      const type = this.getDatePickerType(column);
      if (column.dateConfig?.valueFormat) {
        return column.dateConfig.valueFormat;
      }
      // 默认值格式
      return type === 'month' ? 'yyyy-MM' : 'yyyy-MM-dd';
    },
    getDatePlaceholder(column) {
      const type = this.getDatePickerType(column);
      if (column.dateConfig?.placeholder) {
        return column.dateConfig.placeholder;
      }
      // 默认占位符
      return type === 'month' ? '请选择月份' : '请选择日期';
    },
    handleEditableClick(event, index, prop) {
      // 阻止事件冒泡
      event.stopPropagation();
      // 进入编辑模式
      this.$emit('enter-edit', { index, prop });
    },
    handleClickableClick(event, row, prop, index) {
      // 阻止事件冒泡
      event.stopPropagation();
      // 发出点击事件
      this.$emit('cell-click', {
        event,
        row,
        prop,
        index,
      });
    },
    handleEditChange(row, prop, index, value) {
      // 获取列配置
      const column = this.column;

      // 如果是日期类型，进行验证
      if (column.editType === 'date' && value) {
        const validatedValue = this.validateDateValue(value, column);
        if (validatedValue !== value) {
          // 如果验证后的值不同，更新行数据
          row[prop] = validatedValue;
          value = validatedValue;
        }
      }

      // 发出值变化事件
      this.$emit('cell-change', {
        row,
        prop,
        index,
        value,
      });
    },
    validateDateValue(value, column) {
      if (!value) return value;

      try {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          console.warn('无效的日期值:', value);
          return '';
        }

        // 检查日期范围限制（如果配置了的话）
        if (column.dateConfig?.minDate) {
          const minDate = new Date(column.dateConfig.minDate);
          if (date < minDate) {
            console.warn('日期不能早于最小日期:', column.dateConfig.minDate);
            return column.dateConfig.minDate;
          }
        }

        if (column.dateConfig?.maxDate) {
          const maxDate = new Date(column.dateConfig.maxDate);
          if (date > maxDate) {
            console.warn('日期不能晚于最大日期:', column.dateConfig.maxDate);
            return column.dateConfig.maxDate;
          }
        }

        return value;
      } catch (error) {
        console.warn('日期验证失败:', error);
        return '';
      }
    },
    handleEditBlur() {
      // 延迟退出编辑，避免与点击事件冲突
      setTimeout(() => {
        this.$emit('exit-edit');
      }, 150);
    },
    handleEditEnter() {
      // 按回车键退出编辑
      this.$emit('exit-edit');
    },
    handleDatePickerFocus() {
      // 日期选择器获得焦点时的处理
      this.$nextTick(() => {
        // 确保弹出层正确显示
        const datePicker = this.$refs.datePicker;
        if (datePicker && datePicker.focus) {
          datePicker.focus();
        }
      });
    },
    handleDatePickerBlur() {
      // 日期选择器失去焦点时的处理
      // 延迟退出编辑，避免与日期选择器弹出层点击事件冲突
      setTimeout(() => {
        this.$emit('exit-edit');
      }, 200);
    },
  },
};
</script>

<style lang="scss" scoped>
.summary-cell {
  font-weight: bold;
  color: #606266;
}

.clickable-cell {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
}

.editable-cell {
  cursor: pointer;
  color: #409eff;
  border-bottom: 1px dashed #e2e2e2;

  &:hover {
    background-color: #f5f7fa;
  }
}

// 编辑状态下的样式
::v-deep .editing-input {
  .el-date-editor {
    .el-input__inner {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }

    .el-input__prefix {
      color: #409eff;
    }
  }
}
</style>
