<!--表单表格列 -->
<template>
  <el-table-column
    :label="column.label"
    :prop="column.prop"
    :width="column.width"
    :min-width="column.minWidth || 120"
    :fixed="column.fixed"
    :align="column.align || 'center'"
  >
    <!-- 如果有子列，递归渲染 -->
    <template v-if="column.children && column.children.length">
      <OgwColumn
        v-for="child in column.children"
        :key="child.prop"
        :column="child"
        :show-index="showIndex"
        :summary-config="summaryConfig"
        :fixed="column.fixed"
        @enter-edit="$emit('enter-edit', $event)"
        @exit-edit="$emit('exit-edit', $event)"
        @cell-change="$emit('cell-change', $event)"
        @cell-click="$emit('cell-click', $event)"
      >
        <!-- 透传插槽 -->
        <template v-for="(_, slotName) in $scopedSlots" #[slotName]="scope">
          <slot :name="slotName" v-bind="scope" />
        </template>
      </OgwColumn>
    </template>

    <!-- 没有子列，渲染单元格内容 -->
    <template v-if="!column.children" v-slot="scope">
      <template v-if="scope.row._isSummaryRow">
        <template
          v-if="isFirstSummaryTextColumn(scope.row._summaryType, column.prop)"
        >
          <span class="summary-text">{{ scope.row._summaryText }}</span>
        </template>
        <template v-else-if="summaryConfig.sumColumns.includes(column.prop)">
          <span class="summary-value">{{ scope.row[column.prop] }}</span>
        </template>
      </template>

      <template v-else>
        <!-- 编辑模式 -->
        <template
          v-if="column.editable && isEditingCell(scope.$index, column.prop)"
        >
          <div class="editing-input" @click.stop>
            <!-- 下拉选择器 -->
            <el-select
              v-if="column.options"
              v-model="scope.row[column.prop]"
              size="small"
              style="width: 100%"
              @change="handleEditChange(scope.row, column.prop, scope.$index, $event)"
              @blur="handleEditBlur"
            >
              <el-option
                v-for="opt in column.options"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
            <!-- 其他编辑组件 -->
            <component
              v-else
              :is="getEditComponent(column)"
              v-model="scope.row[column.prop]"
              size="small"
              @blur="handleEditBlur"
              @keyup.enter="handleEditEnter"
              @change="handleEditChange(scope.row, column.prop, scope.$index, $event)"
              style="width: 100%"
            />
          </div>
        </template>

        <!-- 可编辑但未在编辑状态 -->
        <template v-else-if="column.editable">
          <span
            class="editable-cell"
            @click="handleEditableClick($event, scope.$index, column.prop)"
          >
            {{ getFormattedValue(column, scope.row) || "-" }}
          </span>
        </template>

        <!-- 可点击但不可编辑 -->
        <template v-else-if="column.clickable">
          <span
            class="clickable-cell"
            @click="handleClickableClick($event, scope.row, column.prop, scope.$index)"
          >
            <slot :name="column.prop" v-bind="scope">
              {{ getFormattedValue(column, scope.row) }}
            </slot>
          </span>
        </template>

        <!-- 普通单元格 -->
        <template v-else>
          <span>
            <slot :name="column.prop" v-bind="scope">
              {{ getFormattedValue(column, scope.row) }}
            </slot>
          </span>
        </template>
      </template>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: "OgwColumn",
  props: {
    column: Object,
    showIndex: Boolean,
    summaryConfig: Object,
    editingCell: {
      type: Object,
      default: () => ({ rowIndex: null, prop: null }),
    },
  },
  methods: {
    isFirstSummaryTextColumn(type, prop) {
      const mergeCols =
        type === "grand"
          ? this.summaryConfig.grandTotalTextMergeColumns
          : type === "sub"
          ? this.summaryConfig.subTotalTextMergeColumns
          : type === "customSub"
          ? this.summaryConfig.customSubTotalTextMergeColumns
          : type === "customGrand"
          ? this.summaryConfig.customGrandTotalTextMergeColumns
          : [];
      return mergeCols.length && mergeCols[0] === prop;
    },
    isEditingCell(index, prop) {
      return (
        this.editingCell.rowIndex === index && this.editingCell.prop === prop
      );
    },
    getFormattedValue(col, row) {
      const value = row[col.prop];
      if (col.formatter) {
        return col.formatter(row, value);
      }
      if (col.options) {
        const match = col.options.find((opt) => opt.value === value);
        return match ? match.label : value;
      }
      return value;
    },
    getEditComponent(column) {
      // 根据列配置返回对应的编辑组件
      if (column.editComponent) {
        return column.editComponent;
      }
      // 默认使用 el-input
      return 'el-input';
    },
    handleEditableClick(event, index, prop) {
      // 阻止事件冒泡
      event.stopPropagation();
      // 进入编辑模式
      this.$emit('enter-edit', { index, prop });
    },
    handleClickableClick(event, row, prop, index) {
      // 阻止事件冒泡
      event.stopPropagation();
      // 发出点击事件
      this.$emit('cell-click', {
        event,
        row,
        prop,
        index,
      });
    },
    handleEditChange(row, prop, index, value) {
      // 发出值变化事件
      this.$emit('cell-change', {
        row,
        prop,
        index,
        value,
      });
    },
    handleEditBlur() {
      // 延迟退出编辑，避免与点击事件冲突
      setTimeout(() => {
        this.$emit('exit-edit');
      }, 150);
    },
    handleEditEnter() {
      // 按回车键退出编辑
      this.$emit('exit-edit');
    },
    handleDatePickerFocus() {
      // 日期选择器获得焦点时的处理
      this.$nextTick(() => {
        // 确保弹出层正确显示
        const datePicker = this.$refs.datePicker;
        if (datePicker && datePicker.focus) {
          datePicker.focus();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.summary-cell {
  font-weight: bold;
  color: #606266;
}

.clickable-cell {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;
}

.editable-cell {
  cursor: pointer;
  color: #409eff;
  border-bottom: 1px dashed #e2e2e2;
}
</style>
